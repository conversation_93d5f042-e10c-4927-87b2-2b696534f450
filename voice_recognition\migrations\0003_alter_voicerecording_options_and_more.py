# Generated by Django 4.2.9 on 2025-03-28 01:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('voice_recognition', '0002_voicerecording_language'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='voicerecording',
            options={'ordering': ['-created_at']},
        ),
        migrations.AddField(
            model_name='voicerecording',
            name='confidence_score',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='voicerecording',
            name='error_message',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='voicerecording',
            name='file_duration',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='voicerecording',
            name='is_public',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='voicerecording',
            name='last_modified',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='voicerecording',
            name='processing_time',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='voicerecording',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='voicerecording',
            name='word_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddIndex(
            model_name='voicerecording',
            index=models.Index(fields=['language'], name='voice_recog_languag_8e73cd_idx'),
        ),
        migrations.AddIndex(
            model_name='voicerecording',
            index=models.Index(fields=['status'], name='voice_recog_status_990ba6_idx'),
        ),
        migrations.AddIndex(
            model_name='voicerecording',
            index=models.Index(fields=['created_at'], name='voice_recog_created_2ce0ab_idx'),
        ),
    ]
