"""
URL configuration for voice_project project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from voice_recognition.views import index, process_voice
from voice_recognition.views import index, process_voice, get_recordings, get_random_letter  
from django.conf import settings
from django.conf.urls.static import static

from voice_recognition import views

from voice_recognition.views import (
    index, process_voice, get_recordings, get_random_letter,
    start_word_challenge, check_letter_progress, get_word_progress
)


urlpatterns = [
    path('admin/', admin.site.urls),   
    path('', index, name='index'),
    path('process_voice/', process_voice, name='process_voice'),
     path('get_random_letter/', get_random_letter, name='get_random_letter'),  # Add this line
      path('start_word_challenge/', start_word_challenge, name='start_word_challenge'),
    path('check_letter_progress/', check_letter_progress, name='check_letter_progress'),
    path('get_word_progress/', get_word_progress, name='get_word_progress'),

     path('get_recordings/', get_recordings, name='get_recordings'),

      path('simple/', views.simple_voice_recognition, name='simple_voice_recognition'),
    path('process_voice_simple/', views.process_voice_simple, name='process_voice_simple'),

] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)



