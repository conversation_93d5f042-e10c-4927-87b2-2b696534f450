<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phonetic Letter Learning App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <header class="bg-blue-600 text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Phonetic Letter Learning</h1>
            <div class="flex items-center space-x-4">
                <select id="language-selector" class="bg-blue-500 text-white p-2 rounded">
                    <option value="en-PH">English (Philippines)</option>
                    <option value="tl-PH">Filipino</option>
                </select>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 flex-grow">
        <div class="bg-white shadow-lg rounded-lg p-6">
            <div class="grid md:grid-cols-2 gap-6">
                <!-- Letter Learning Section -->
                <div class="space-y-4">
                    <div class="bg-blue-50 p-4 rounded-lg text-center">
                        <h2 class="text-xl font-semibold mb-4">Current Letter Challenge</h2>
                        <div id="current-letter" class="text-6xl font-bold text-blue-600 mb-4">-</div>
                        <p id="letter-pronunciation" class="text-gray-600">Pronunciation</p>
                        <button id="new-letter-btn" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition">
                            Get New Letter
                        </button>
                    </div>

                    <!-- Recording Controls -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Record Your Pronunciation</h3>
                        <div class="flex space-x-4 items-center">
                            <button id="start-recording" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition">
                                <i class="fas fa-microphone mr-2"></i>Start Recording
                            </button>
                            <button id="stop-recording" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition" disabled>
                                <i class="fas fa-stop mr-2"></i>Stop Recording
                            </button>
                            <audio id="audio-playback" controls class="hidden"></audio>
                        </div>
                    </div>
                </div>

                <!-- Results and Feedback Section -->
                <div class="space-y-4">
                    <div id="result-section" class="bg-gray-50 p-4 rounded-lg hidden">
                        <h3 class="text-lg font-semibold mb-4">Pronunciation Result</h3>
                        <div id="result-details" class="space-y-2">
                            <p>Recognized Text: <span id="recognized-text" class="font-medium"></span></p>
                            <p>Pronunciation Accuracy: <span id="accuracy-result" class="font-medium"></span></p>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Recent Recordings</h3>
                        <ul id="recordings-list" class="space-y-2 max-h-64 overflow-y-auto">
                            <!-- Recordings will be dynamically populated -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- Add this section after the existing grid in the main content -->
        <div class="mt-8 bg-white shadow-lg rounded-lg p-6">
            <h2 class="text-2xl font-semibold mb-6">Word Challenge</h2>
            <div class="space-y-4">
                <!-- Word Input -->
                <div class="flex space-x-4">
                    <input type="text" id="word-input"
                           class="flex-1 border rounded-lg px-4 py-2"
                           placeholder="Enter a word to practice">
                    <button id="start-word-challenge"
                            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                        Start Challenge
                    </button>
                </div>

                <!-- Word Progress -->
                <div id="word-challenge-section" class="hidden">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-2">Current Progress</h3>
                        <div class="flex items-center space-x-4">
                            <div class="flex-1 bg-gray-200 rounded-full h-4">
                                <div id="progress-bar"
                                     class="bg-blue-600 h-4 rounded-full transition-all duration-300"
                                     style="width: 0%">
                                </div>
                            </div>
                            <span id="progress-text" class="text-sm font-medium">0%</span>
                        </div>
                        <div id="completed-letters" class="mt-4 flex flex-wrap gap-2">
                            <!-- Completed letters will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <footer class="bg-blue-600 text-white p-4 text-center">
        <p>&copy; 2024 Phonetic Letter Learning App</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const languageSelector = document.getElementById('language-selector');
            const currentLetter = document.getElementById('current-letter');
            const letterPronunciation = document.getElementById('letter-pronunciation');
            const newLetterBtn = document.getElementById('new-letter-btn');
            const startRecordingBtn = document.getElementById('start-recording');
            const stopRecordingBtn = document.getElementById('stop-recording');
            const audioPlayback = document.getElementById('audio-playback');
            const resultSection = document.getElementById('result-section');
            const recognizedTextSpan = document.getElementById('recognized-text');
            const accuracyResultSpan = document.getElementById('accuracy-result');
            const recordingsList = document.getElementById('recordings-list');

            // Word Challenge Elements
            const wordInput = document.getElementById('word-input');
            const startWordChallengeBtn = document.getElementById('start-word-challenge');
            const wordChallengeSection = document.getElementById('word-challenge-section');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const completedLetters = document.getElementById('completed-letters');

            let mediaRecorder;
            let audioChunks = [];
            let currentTargetLetter = '';
            let isWordChallengeActive = false;

async function startWordChallenge() {
    const word = wordInput.value.trim();
    if (!word) {
        alert('Please enter a word');
        return;
    }

    const formData = new FormData();
    formData.append('word', word);
    formData.append('language', languageSelector.value);

    try {
        const response = await fetch('/start_word_challenge/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || `HTTP error! status: ${response.status}`);
        }

        if (data.error) {
            throw new Error(data.error);
        }

        isWordChallengeActive = true;
        wordChallengeSection.classList.remove('hidden');
        updateWordProgress(data);
        
        // Update current letter display
        if (data.current_letter) {
            currentLetter.textContent = data.current_letter.letter;
            letterPronunciation.textContent = data.current_letter.pronunciation;
            currentTargetLetter = data.current_letter.letter;
        }

    } catch (error) {
        console.error('Error starting word challenge:', error);
        alert(error.message || 'Failed to start word challenge. Please try again.');
    }
}

// Make sure this getCookie function is present
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

            function updateWordProgress(data) {
                progressBar.style.width = `${data.progress}%`;
                progressText.textContent = `${Math.round(data.progress)}%`;

                completedLetters.innerHTML = data.completed_letters
                    .map(letter => `<span class="bg-green-100 text-green-800 px-3 py-1 rounded-full">${letter}</span>`)
                    .join('');
            }

            // Letter Practice Functions
            function fetchRandomLetter() {
                const language = languageSelector.value;
                fetch(`/get_random_letter/?language=${language}`)
                    .then(response => response.json())
                    .then(data => {
                        currentLetter.textContent = data.letter;
                        letterPronunciation.textContent = data.pronunciation;
                        currentTargetLetter = data.letter;
                    });
            }

            function initializeRecording() {
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(stream => {
                        mediaRecorder = new MediaRecorder(stream);

                        mediaRecorder.ondataavailable = (event) => {
                            audioChunks.push(event.data);
                        };

                        mediaRecorder.onstop = async () => {
                            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                            const audioUrl = URL.createObjectURL(audioBlob);
                            audioPlayback.src = audioUrl;
                            audioPlayback.classList.remove('hidden');

                            const formData = new FormData();
                            formData.append('audio', audioBlob, 'recording.webm');
                            formData.append('language', languageSelector.value);

                            try {
                                let response;
                                if (isWordChallengeActive) {
                                    response = await fetch('/check_letter_progress/', {
                                        method: 'POST',
                                        body: formData
                                    });
                                } else {
                                    formData.append('target_letter', currentTargetLetter);
                                    response = await fetch('/process_voice/', {
                                        method: 'POST',
                                        body: formData
                                    });
                                }

                                const data = await response.json();

                                if (isWordChallengeActive && data.status === 'success') {
                                    updateWordProgress(data);
                                    if (data.is_completed) {
                                        alert('Congratulations! You completed the word challenge!');
                                        wordInput.value = '';
                                        wordChallengeSection.classList.add('hidden');
                                        isWordChallengeActive = false;
                                        fetchRandomLetter();
                                    } else if (data.next_letter) {
                                        currentLetter.textContent = data.next_letter.letter;
                                        letterPronunciation.textContent = data.next_letter.pronunciation;
                                        currentTargetLetter = data.next_letter.letter;
                                    }
                                } else {
                                    // Regular letter practice feedback
                                    resultSection.classList.remove('hidden');
                                    recognizedTextSpan.textContent = data.text || '';
                                    accuracyResultSpan.textContent = data.correct ? 'Correct ✅' : 'Incorrect ❌';
                                }

                                fetchRecordings();
                            } catch (error) {
                                console.error('Error processing recording:', error);
                                alert('Failed to process recording');
                            }

                            audioChunks = [];
                        };

                        startRecordingBtn.disabled = false;
                        stopRecordingBtn.disabled = true;
                    });
            }

            function fetchRecordings() {
                fetch('/get_recordings/')
                    .then(response => response.json())
                    .then(recordings => {
                        recordingsList.innerHTML = '';
                        recordings.slice(0, 5).forEach(recording => {
                            const li = document.createElement('li');
                            li.classList.add('bg-white', 'p-2', 'rounded', 'shadow-sm');
                            li.innerHTML = `
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">${recording.text}</span>
                                    ${recording.audio_url ?
                                    `<audio controls class="w-full max-w-[150px]">
                                        <source src="${recording.audio_url}" type="audio/wav">
                                    </audio>` :
                                    '<span class="text-gray-500">No audio</span>'}
                                </div>
                            `;
                            recordingsList.appendChild(li);
                        });
                    });
            }

            // Event Listeners
            startWordChallengeBtn.addEventListener('click', startWordChallenge);
            newLetterBtn.addEventListener('click', () => {
                isWordChallengeActive = false;
                fetchRandomLetter();
            });
            languageSelector.addEventListener('change', fetchRandomLetter);

            startRecordingBtn.addEventListener('click', () => {
                audioChunks = [];
                mediaRecorder.start();
                startRecordingBtn.disabled = true;
                stopRecordingBtn.disabled = false;
                audioPlayback.classList.add('hidden');
                resultSection.classList.add('hidden');
            });

            stopRecordingBtn.addEventListener('click', () => {
                mediaRecorder.stop();
                startRecordingBtn.disabled = false;
                stopRecordingBtn.disabled = true;
            });

            // Initialize app
            initializeRecording();
            fetchRandomLetter();
            fetchRecordings();
        });
    </script>
</body>
</html>