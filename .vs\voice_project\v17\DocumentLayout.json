{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\voice_project\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\voice_project\\voice_recognition\\models.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:voice_recognition\\models.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\voice_project\\voice_recognition\\views.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:voice_recognition\\views.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\voice_project\\voice_project\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:voice_project\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\voice_project\\voice_recognition\\templates\\simple.html||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:voice_recognition\\templates\\simple.html||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\voice_project\\voice_recognition\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:voice_recognition\\urls.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\voice_project\\voice_recognition\\templates\\index.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:voice_recognition\\templates\\index.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\voice_project\\voice_project\\settings.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:voice_project\\settings.py||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1a46fd64-28d5-0019-8eb3-17a02d419b53}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "simple.html", "DocumentMoniker": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\templates\\simple.html", "RelativeDocumentMoniker": "voice_recognition\\templates\\simple.html", "ToolTip": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\templates\\simple.html", "RelativeToolTip": "voice_recognition\\templates\\simple.html", "ViewState": "AgIAAK8AAAAAAAAAAAAAAMwAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-03-28T02:27:02.105Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "models.py", "DocumentMoniker": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\models.py", "RelativeDocumentMoniker": "voice_recognition\\models.py", "ToolTip": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\models.py", "RelativeToolTip": "voice_recognition\\models.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-28T00:39:52.67Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "index.html", "DocumentMoniker": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\templates\\index.html", "RelativeDocumentMoniker": "voice_recognition\\templates\\index.html", "ToolTip": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\templates\\index.html", "RelativeToolTip": "voice_recognition\\templates\\index.html", "ViewState": "AgIAAD4BAAAAAAAAAAAAAFsBAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-03-28T00:37:49.353Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "urls.py", "DocumentMoniker": "C:\\Users\\<USER>\\voice_project\\voice_project\\urls.py", "RelativeDocumentMoniker": "voice_project\\urls.py", "ToolTip": "C:\\Users\\<USER>\\voice_project\\voice_project\\urls.py", "RelativeToolTip": "voice_project\\urls.py", "ViewState": "AgIAAA4AAAAAAAAAAADwvxcAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-28T00:36:54.391Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "urls.py", "DocumentMoniker": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\urls.py", "RelativeDocumentMoniker": "voice_recognition\\urls.py", "ToolTip": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\urls.py", "RelativeToolTip": "voice_recognition\\urls.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-28T00:36:40.448Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "views.py", "DocumentMoniker": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\views.py", "RelativeDocumentMoniker": "voice_recognition\\views.py", "ToolTip": "C:\\Users\\<USER>\\voice_project\\voice_recognition\\views.py", "RelativeToolTip": "voice_recognition\\views.py", "ViewState": "AgIAAJYBAAAAAAAAAADgv58BAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-28T00:36:05.153Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "settings.py", "DocumentMoniker": "C:\\Users\\<USER>\\voice_project\\voice_project\\settings.py", "RelativeDocumentMoniker": "voice_project\\settings.py", "ToolTip": "C:\\Users\\<USER>\\voice_project\\voice_project\\settings.py", "RelativeToolTip": "voice_project\\settings.py", "ViewState": "AgIAAHsAAAAAAAAAAADwv5cAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-03-28T00:35:47.371Z"}]}]}]}