from django.db import models

class VoiceRecording(models.Model):
    LANGUAGE_CHOICES = [
        ('en-PH', 'Filipino English'),
        ('tl-PH', 'Filipino'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    audio_file = models.FileField(upload_to='audio_files/')
    recognized_text = models.TextField(blank=True, null=True)
    phonemes = models.TextField(blank=True, null=True)
    letters = models.TextField(blank=True, null=True)
    language = models.CharField(max_length=5, choices=LANGUAGE_CHOICES, default='en-PH')
    created_at = models.DateTimeField(auto_now_add=True)
    
    # New fields
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    error_message = models.TextField(blank=True, null=True)
    processing_time = models.FloatField(null=True, blank=True)  # in seconds
    file_duration = models.FloatField(null=True, blank=True)    # in seconds
    confidence_score = models.FloatField(null=True, blank=True)
    word_count = models.IntegerField(default=0)
    is_public = models.BooleanField(default=True)
    last_modified = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['language']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Recording {self.id} - {self.recognized_text[:50]} ({self.language})"

    def get_word_count(self):
        if self.recognized_text:
            return len(self.recognized_text.split())
        return 0

    def save(self, *args, **kwargs):
        self.word_count = self.get_word_count()
        super().save(*args, **kwargs)