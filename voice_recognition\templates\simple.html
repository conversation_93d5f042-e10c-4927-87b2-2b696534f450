﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Recognition</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .result-box {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
        }

        .phoneme {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            background-color: #e9ecef;
            border-radius: 3px;
            font-size: 0.9em;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }

        #startRecording {
            background-color: #28a745;
            color: white;
        }

        #stopRecording {
            background-color: #dc3545;
            color: white;
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .language-selector {
            margin: 20px 0;
        }

        select {
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
        }

        .recording-indicator {
            display: none;
            color: red;
            margin-left: 10px;
        }

        .recording .recording-indicator {
            display: inline;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            50% { opacity: 0; }
        }
    </style>
</head>
<body>
    <h1>Voice Recognition (Phoneme & Letter Detection)</h1>

    <div class="language-selector">
        <label for="language">Language:</label>
        <select id="language">
            <option value="tl-PH">Filipino</option>
            <option value="en-PH">Filipino English</option>
        </select>
    </div>

    <div>
        <button id="startRecording">Start Recording</button>
        <button id="stopRecording" disabled>Stop Recording</button>
        <span class="recording-indicator">● Recording</span>
    </div>

    <div class="result-box">
        <p><b>Recognized Text:</b> <span id="recognizedText">...</span></p>
        <p><b>Extracted Letters:</b> <span id="letters">...</span></p>
        <p><b>Phonemes:</b> <div id="phonemes" style="display: inline-block">...</div></p>
    </div>

    <script>
        let mediaRecorder;
        let audioChunks = [];
        const body = document.body;

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        async function processVoice(audioBlob) {
            const formData = new FormData();
            formData.append("audio", audioBlob, "recording.webm");
            formData.append("language", document.getElementById("language").value);

            try {
                const response = await fetch("/process_voice_simple/", {
                    method: "POST",
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                updateResults(result);
            } catch (error) {
                console.error("Error:", error);
                showError("Error processing audio");
            }
        }

        function updateResults(result) {
            document.getElementById("recognizedText").innerText = result.text;
            document.getElementById("letters").innerText = result.letters.join(", ");
            
            const phonemesContainer = document.getElementById("phonemes");
            phonemesContainer.innerHTML = result.phonemes
                .map(phoneme => `<span class="phoneme">${phoneme}</span>`)
                .join(" ");
        }

        function showError(message) {
            document.getElementById("recognizedText").innerText = message;
            document.getElementById("letters").innerText = "Error";
            document.getElementById("phonemes").innerText = "Error";
        }

        document.getElementById("startRecording").addEventListener("click", async function() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm'
                });
                
                audioChunks = [];
                mediaRecorder.addEventListener("dataavailable", event => {
                    audioChunks.push(event.data);
                });

                mediaRecorder.start();
                body.classList.add('recording');
                document.getElementById("startRecording").disabled = true;
                document.getElementById("stopRecording").disabled = false;
            } catch (error) {
                console.error("Error accessing microphone:", error);
                alert("Error accessing microphone. Please ensure microphone permissions are granted.");
            }
        });

        document.getElementById("stopRecording").addEventListener("click", function() {
            mediaRecorder.stop();
            mediaRecorder.addEventListener("stop", () => {
                body.classList.remove('recording');
                const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                processVoice(audioBlob);
                
                document.getElementById("startRecording").disabled = false;
                document.getElementById("stopRecording").disabled = true;
                
                // Stop all tracks
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
            });
        });
    </script>
</body>
</html>