﻿from django.shortcuts import render
import speech_recognition as sr

from rest_framework.parsers import MultiPartParser
from rest_framework.decorators import api_view
from rest_framework.response import Response
import wave
import time
import torchaudio
import torch
from transformers import (
    Wav2Vec2Processor, 
    Wav2Vec2ForCTC,
    Wav2Vec2CTCTokenizer,
    Wav2Vec2FeatureExtractor
)
import os
from pydub import AudioSegment
from .models import VoiceRecording
import warnings
import logging
from django.core.files.base import ContentFile
from pathlib import Path
import soundfile as sf
import numpy as np
import random

# Suppress warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

# Load models for text detection only
text_model_name = "facebook/wav2vec2-base-960h"

try:
    text_processor = Wav2Vec2Processor.from_pretrained(text_model_name)
    text_model = Wav2Vec2ForCTC.from_pretrained(text_model_name)
    logger.info("Text model loaded successfully")
except Exception as e:
    logger.error(f"Failed to load text model: {str(e)}")
    text_processor = text_model = None

# Filipino and English letters with their phonetic pronunciations
LETTER_SETS = {
    'en-PH': {
        'A': 'ay', 'B': 'bee', 'C': 'see', 'D': 'dee', 'E': 'ee',
        'F': 'ef', 'G': 'jee', 'H': 'aych', 'I': 'eye', 'J': 'jay',
        'K': 'kay', 'L': 'el', 'M': 'em', 'N': 'en', 'O': 'oh',
        'P': 'pee', 'Q': 'kyoo', 'R': 'ar', 'S': 'es', 'T': 'tee',
        'U': 'you', 'V': 'vee', 'W': 'double-you', 'X': 'eks',
        'Y': 'why', 'Z': 'zee'
    },
    'tl-PH': {
        'A': 'ah', 'B': 'beh', 'C': 'see', 'D': 'deh', 'E': 'eh',
        'F': 'ef', 'G': 'geh', 'H': 'hah', 'I': 'ee', 'J': 'hay',
        'K': 'kah', 'L': 'el', 'M': 'em', 'N': 'en', 'Ñ': 'enye',
        'NG': 'nga', 'O': 'oh', 'P': 'peh', 'Q': 'kyoo', 'R': 'er',
        'S': 'es', 'T': 'teh', 'U': 'oo', 'V': 'veh', 'W': 'weh',
        'X': 'eks', 'Y': 'yeh', 'Z': 'zeh'
    }
}

def process_audio_for_model(speech, processor, model):
    try:
        input_values = processor(speech, sampling_rate=16000, return_tensors="pt").input_values
        logits = model(input_values).logits
        predicted_ids = torch.argmax(logits, dim=-1)
        return processor.batch_decode(predicted_ids)[0]
    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        return ""

@api_view(['GET'])
def get_random_letter(request):
    language = request.GET.get('language', 'en-PH')
    letters = list(LETTER_SETS[language].keys())
    random_letter = random.choice(letters)
    return Response({
        'letter': random_letter,
        'pronunciation': LETTER_SETS[language][random_letter]
    })

@api_view(['POST'])
def process_voice(request):
    if 'audio' not in request.FILES:
        return Response({'error': 'No audio file provided'}, status=400)

    if not all([text_processor, text_model]):
        return Response({'error': 'Models not properly initialized'}, status=500)

    target_letter = request.POST.get('target_letter', '')
    language = request.POST.get('language', 'en-PH')

    BASE_DIR = Path(__file__).resolve().parent.parent
    TEMP_DIR = BASE_DIR / 'temp'
    TEMP_DIR.mkdir(exist_ok=True)
    
    timestamp = int(time.time() * 1000)
    webm_path = TEMP_DIR / f'temp_audio_{timestamp}.webm'
    wav_path = TEMP_DIR / f'temp_audio_{timestamp}.wav'

    try:
        # Save and convert audio file
        audio_file = request.FILES['audio']
        with open(webm_path, 'wb+') as f:
            for chunk in audio_file.chunks():
                f.write(chunk)
        
        audio = AudioSegment.from_file(str(webm_path))
        audio = audio.set_channels(1).set_frame_rate(16000)
        audio.export(str(wav_path), format="wav", parameters=["-acodec", "pcm_s16le"])

        # Speech recognition
        recognizer = sr.Recognizer()
        with sr.AudioFile(str(wav_path)) as source:
            audio_data = recognizer.record(source)
        try:
            text = recognizer.recognize_google(audio_data, language=language)
        except sr.UnknownValueError:
            text = "Could not understand the audio"

        # Generate letters and phonemes from recognized text
        letters = text.upper()
        phonemes = [char.lower() for char in text if char.isalnum()]

        # Check pronunciation if target letter is provided
        correct = False
        expected_pronunciation = LETTER_SETS[language].get(target_letter, '')
        if expected_pronunciation:
            text_lower = text.lower().strip()
            correct = (expected_pronunciation.lower() in text_lower or 
                      target_letter.lower() in text_lower)

        # Store in database
        recording = VoiceRecording.objects.create(
            recognized_text=text,
            letters=','.join(list(letters)),
            phonemes=','.join(phonemes),
            language=language,
            confidence_score=1.0 if correct else 0.0
        )
        
        with open(wav_path, 'rb') as f:
            recording.audio_file.save(f'recording_{recording.id}.wav', ContentFile(f.read()))
        
        response_data = {
            'text': text,
            'letters': list(letters),
            'phonemes': phonemes,
            'recording_id': recording.id,
            'audio_url': recording.audio_file.url if recording.audio_file else None
        }

        # Add pronunciation check results if target letter was provided
        if target_letter:
            response_data.update({
                'target_letter': target_letter,
                'correct': correct,
                'expected_pronunciation': expected_pronunciation
            })

        return Response(response_data)

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return Response({'error': str(e)}, status=500)

    finally:
        # Cleanup
        for path in [webm_path, wav_path]:
            if path.exists():
                path.unlink()

@api_view(['GET'])
def get_recordings(request):
    recordings = VoiceRecording.objects.all().order_by('-created_at')
    data = [
        {
            "id": rec.id,
            "text": rec.recognized_text,
            "letters": rec.letters.split(',') if rec.letters else [],
            "audio_url": rec.audio_file.url if rec.audio_file else None,
            "created_at": rec.created_at
        }
        for rec in recordings
    ]
    return Response(data)

def index(request):
    return render(request, 'index.html')



# Add these new functions after the existing imports

@api_view(['POST'])
def start_word_challenge(request):
    word = request.POST.get('word', '').upper()
    language = request.POST.get('language', 'en-PH')
    
    if not word:
        return Response({'error': 'No word provided'}, status=400)
    
    # Create a list of letters with their pronunciations
    letter_sequence = []
    for letter in word:
        if letter in LETTER_SETS[language]:
            letter_sequence.append({
                'letter': letter,
                'pronunciation': LETTER_SETS[language][letter]
            })
        else:
            return Response({'error': f'Invalid letter "{letter}" in word'}, status=400)
    
    if not letter_sequence:
        return Response({'error': 'No valid letters in word'}, status=400)
    
    # Store the word challenge in the session
    request.session['word_challenge'] = {
        'word': word,
        'current_index': 0,
        'completed_letters': [],
        'language': language
    }
    
    return Response({
        'word': word,
        'total_letters': len(letter_sequence),
        'current_letter': letter_sequence[0],
        'progress': 0,
        'completed_letters': []
    })

def process_voice_for_letter(audio_file, target_letter, language, request):
    """Helper function to process voice for a single letter"""
    from django.http import QueryDict
    from django.core.files.uploadedfile import UploadedFile
    
    # Create a new QueryDict with the parameters we need
    post_data = QueryDict('', mutable=True)
    post_data.update({
        'target_letter': target_letter,
        'language': language
    })
    
    # Create a new request with our parameters
    from django.http import HttpRequest
    new_request = HttpRequest()
    new_request.method = 'POST'
    new_request.POST = post_data
    new_request.FILES = {'audio': audio_file}
    new_request.META = request.META
    
    # Process the audio
    try:
        # Call the existing process_voice function
        response = process_voice(new_request)
        recognized_text = response.data.get('text', '').lower()
        
        # If the audio couldn't be understood, mark it as incorrect
        if recognized_text == "could not understand the audio":
            return {
                'correct': False,
                'recognized_text': recognized_text,
                'expected_pronunciation': LETTER_SETS[language].get(target_letter, ''),
                'error': 'Could not understand the audio. Please try again.'
            }
        
        # Get the expected pronunciation
        expected_pronunciation = LETTER_SETS[language].get(target_letter, '').lower()
        
        # Check if either the letter itself or its pronunciation is in the recognized text
        is_correct = (
            target_letter.lower() in recognized_text or 
            expected_pronunciation in recognized_text or
            any(word.strip() == expected_pronunciation for word in recognized_text.split())
        )
        
        return {
            'correct': is_correct,
            'recognized_text': recognized_text,
            'expected_pronunciation': expected_pronunciation
        }
    except Exception as e:
        logger.error(f"Error in process_voice_for_letter: {str(e)}")
        return {
            'correct': False,
            'recognized_text': '',
            'error': str(e)
        }

@api_view(['POST'])
def check_letter_progress(request):
    word_challenge = request.session.get('word_challenge')
    
    if not word_challenge:
        return Response({'error': 'No active word challenge'}, status=400)
    
    word = word_challenge['word']
    current_index = word_challenge['current_index']
    language = word_challenge['language']
    completed_letters = word_challenge['completed_letters']
    
    if 'audio' not in request.FILES:
        return Response({'error': 'No audio file provided'}, status=400)
    
    current_letter = word[current_index]
    
    # Process the audio for the current letter
    result = process_voice_for_letter(
        request.FILES['audio'],
        current_letter,
        language,
        request
    )
    
    if result.get('error'):
        return Response({
            'status': 'error',
            'message': result['error']
        }, status=500)
    
    if result.get('correct'):
        # Update progress
        completed_letters.append(current_letter)
        current_index += 1
        
        # Update session
        word_challenge.update({
            'current_index': current_index,
            'completed_letters': completed_letters
        })
        request.session['word_challenge'] = word_challenge
        
        # Prepare next letter or complete
        if current_index < len(word):
            next_letter = {
                'letter': word[current_index],
                'pronunciation': LETTER_SETS[language][word[current_index]]
            }
        else:
            next_letter = None
            
        return Response({
            'status': 'success',
            'progress': (current_index / len(word)) * 100,
            'completed_letters': completed_letters,
            'next_letter': next_letter,
            'is_completed': current_index >= len(word),
            'recognized_text': result['recognized_text']
        })
    
    return Response({
        'status': 'retry',
        'message': 'Incorrect pronunciation, please try again',
        'current_letter': {
            'letter': current_letter,
            'pronunciation': LETTER_SETS[language][current_letter]
        },
        'recognized_text': result['recognized_text'],
        'expected_pronunciation': result['expected_pronunciation']
    })

@api_view(['GET'])
def get_word_progress(request):
    word_challenge = request.session.get('word_challenge')
    
    if not word_challenge:
        return Response({'error': 'No active word challenge'}, status=400)
    
    return Response({
        'word': word_challenge['word'],
        'progress': (word_challenge['current_index'] / len(word_challenge['word'])) * 100,
        'completed_letters': word_challenge['completed_letters'],
        'current_index': word_challenge['current_index']
    })





















from django.shortcuts import render
from django.views.decorators.csrf import ensure_csrf_cookie
import speech_recognition as sr
from rest_framework.decorators import api_view
from rest_framework.response import Response
from pydub import AudioSegment
import logging
from pathlib import Path
import time
from django.core.files.base import ContentFile
from .models import VoiceRecording

logger = logging.getLogger(__name__)

@ensure_csrf_cookie
def simple_voice_recognition(request):
    """Render the simple voice recognition interface"""
    return render(request, 'simple.html')

@api_view(['POST'])
def process_voice_simple(request):
    """Process voice recording and return text, letters, and phonemes"""
    if 'audio' not in request.FILES:
        return Response({'error': 'No audio file provided'}, status=400)

    language = request.POST.get('language', 'en-PH')
    
    BASE_DIR = Path(__file__).resolve().parent.parent
    TEMP_DIR = BASE_DIR / 'temp'
    TEMP_DIR.mkdir(exist_ok=True)
    
    timestamp = int(time.time() * 1000)
    webm_path = TEMP_DIR / f'temp_audio_{timestamp}.webm'
    wav_path = TEMP_DIR / f'temp_audio_{timestamp}.wav'

    try:
        # Save and convert audio file
        audio_file = request.FILES['audio']
        with open(webm_path, 'wb+') as f:
            for chunk in audio_file.chunks():
                f.write(chunk)
        
        # Convert to WAV
        audio = AudioSegment.from_file(str(webm_path))
        audio = audio.set_channels(1).set_frame_rate(16000)
        audio.export(str(wav_path), format="wav", parameters=["-acodec", "pcm_s16le"])

        # Speech recognition
        recognizer = sr.Recognizer()
        with sr.AudioFile(str(wav_path)) as source:
            audio_data = recognizer.record(source)
        
        try:
            text = recognizer.recognize_google(audio_data, language=language)
        except sr.UnknownValueError:
            text = "Could not understand the audio"
        except sr.RequestError as e:
            return Response({'error': f'Speech recognition error: {str(e)}'}, status=500)

        # Generate letters and phonemes
        letters = [char.upper() for char in text if char.isalnum()]
        phonemes = [char.lower() for char in text if char.isalnum()]

        # Store in database
        recording = VoiceRecording.objects.create(
            recognized_text=text,
            letters=','.join(letters),
            phonemes=','.join(phonemes),
            language=language
        )
        
        # Save audio file
        with open(wav_path, 'rb') as f:
            recording.audio_file.save(f'recording_{recording.id}.wav', ContentFile(f.read()))

        return Response({
            'text': text,
            'letters': letters,
            'phonemes': phonemes,
            'recording_id': recording.id
        })

    except Exception as e:
        logger.error(f"Error processing voice: {str(e)}")
        return Response({'error': str(e)}, status=500)

    finally:
        # Cleanup temporary files
        for path in [webm_path, wav_path]:
            if path.exists():
                path.unlink()